import React, { useState, useEffect, useRef } from 'react';
import { aiInterviewV2EvaluationsApi } from '../services/api';

const AIInterviewV2EvaluationRunner = ({ selectedDatasetId, onRun, onPollingUpdate }) => {
  const [running, setRunning] = useState(false);
  const [lastResult, setLastResult] = useState(null);
  const [pollingEvaluations, setPollingEvaluations] = useState(new Set());
  
  // Model and temperature settings
  const [transcribeModel, setTranscribeModel] = useState('gemini-2.5-pro');
  const [transcribeTemperature, setTranscribeTemperature] = useState(0.3);
  const [analysisModel, setAnalysisModel] = useState('gemini-2.5-pro');
  const [analysisTemperature, setAnalysisTemperature] = useState(0.3);

  const pollingIntervals = useRef(new Map());

  // Cleanup polling intervals on unmount
  useEffect(() => {
    return () => {
      pollingIntervals.current.forEach(intervalId => clearInterval(intervalId));
      pollingIntervals.current.clear();
    };
  }, []);

  const startPolling = (evaluationId) => {
    if (pollingIntervals.current.has(evaluationId)) {
      return; // Already polling this evaluation
    }

    setPollingEvaluations(prev => new Set([...prev, evaluationId]));

    const intervalId = setInterval(async () => {
      try {
        const response = await aiInterviewV2EvaluationsApi.getStatus(evaluationId);
        const evaluation = response.data;

        if (evaluation.status === 'completed' || evaluation.status === 'error') {
          // Stop polling
          clearInterval(intervalId);
          pollingIntervals.current.delete(evaluationId);
          setPollingEvaluations(prev => {
            const newSet = new Set(prev);
            newSet.delete(evaluationId);
            return newSet;
          });

          // Notify parent component
          if (onPollingUpdate) {
            onPollingUpdate(evaluation);
          }
        }
      } catch (error) {
        console.error('Error polling evaluation status:', error);
        // Continue polling even if there's an error
      }
    }, 30000); // Poll every 30 seconds

    pollingIntervals.current.set(evaluationId, intervalId);
  };

  const handleRun = async () => {
    if (!selectedDatasetId) {
      alert('Please select a dataset first');
      return;
    }

    try {
      setRunning(true);
      const result = await onRun(
        selectedDatasetId, 
        transcribeModel, 
        transcribeTemperature, 
        analysisModel, 
        analysisTemperature
      );
      setLastResult(result);

      // Start polling if the result has "in_progress" status
      if (result && result.status === "in_progress") {
        startPolling(result.id);
      }
    } catch (error) {
      alert('Failed to run AI Interview V2 evaluation');
    } finally {
      setRunning(false);
    }
  };

  const modelOptions = [
    { value: 'gemini-2.5-pro', label: 'Gemini 2.5 Pro' },
    { value: 'gemini-2.5-flash', label: 'Gemini 2.5 Flash' }
  ];

  return (
    <div style={{
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '20px',
      backgroundColor: '#f8f9fa'
    }}>
      <h3 style={{ marginTop: 0, marginBottom: '20px' }}>🎤 AI Interview V2 Evaluation Runner</h3>
      
      {/* Model and Temperature Settings */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: '1fr 1fr', 
        gap: '20px', 
        marginBottom: '20px',
        padding: '15px',
        backgroundColor: 'white',
        borderRadius: '6px',
        border: '1px solid #e9ecef'
      }}>
        {/* Transcription Settings */}
        <div>
          <h4 style={{ margin: '0 0 15px 0', color: '#495057' }}>📝 Transcription Settings</h4>
          
          <div style={{ marginBottom: '15px' }}>
            <label style={{ 
              display: 'block', 
              marginBottom: '5px', 
              fontWeight: '500',
              fontSize: '14px'
            }}>
              Model:
            </label>
            <select
              value={transcribeModel}
              onChange={(e) => setTranscribeModel(e.target.value)}
              style={{
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #ced4da',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            >
              {modelOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label style={{ 
              display: 'block', 
              marginBottom: '5px', 
              fontWeight: '500',
              fontSize: '14px'
            }}>
              Temperature: {transcribeTemperature}
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={transcribeTemperature}
              onChange={(e) => setTranscribeTemperature(parseFloat(e.target.value))}
              style={{
                width: '100%',
                marginBottom: '5px'
              }}
            />
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              fontSize: '12px', 
              color: '#6c757d' 
            }}>
              <span>0 (Deterministic)</span>
              <span>1 (Creative)</span>
            </div>
          </div>
        </div>

        {/* Analysis Settings */}
        <div>
          <h4 style={{ margin: '0 0 15px 0', color: '#495057' }}>🔍 Analysis Settings</h4>
          
          <div style={{ marginBottom: '15px' }}>
            <label style={{ 
              display: 'block', 
              marginBottom: '5px', 
              fontWeight: '500',
              fontSize: '14px'
            }}>
              Model:
            </label>
            <select
              value={analysisModel}
              onChange={(e) => setAnalysisModel(e.target.value)}
              style={{
                width: '100%',
                padding: '8px 12px',
                border: '1px solid #ced4da',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            >
              {modelOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label style={{ 
              display: 'block', 
              marginBottom: '5px', 
              fontWeight: '500',
              fontSize: '14px'
            }}>
              Temperature: {analysisTemperature}
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={analysisTemperature}
              onChange={(e) => setAnalysisTemperature(parseFloat(e.target.value))}
              style={{
                width: '100%',
                marginBottom: '5px'
              }}
            />
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              fontSize: '12px', 
              color: '#6c757d' 
            }}>
              <span>0 (Deterministic)</span>
              <span>1 (Creative)</span>
            </div>
          </div>
        </div>
      </div>

      {/* Run Button */}
      <button
        onClick={handleRun}
        disabled={running || !selectedDatasetId}
        style={{
          backgroundColor: running ? '#6c757d' : '#007bff',
          color: 'white',
          border: 'none',
          padding: '12px 24px',
          borderRadius: '6px',
          cursor: running || !selectedDatasetId ? 'not-allowed' : 'pointer',
          fontSize: '16px',
          fontWeight: '500',
          width: '100%'
        }}
      >
        {running ? 'Running AI Interview V2 Evaluation...' : 'Run AI Interview V2 Evaluation'}
      </button>

      {/* Status Information */}
      {pollingEvaluations.size > 0 && (
        <div style={{
          marginTop: '15px',
          padding: '12px',
          backgroundColor: '#fff3cd',
          border: '1px solid #ffeaa7',
          borderRadius: '6px',
          fontSize: '14px'
        }}>
          <strong>⏳ Background Processing:</strong> {pollingEvaluations.size} evaluation(s) in progress...
          <br />
          <small style={{ color: '#856404' }}>
            Results will appear automatically when processing completes (polling every 30 seconds)
          </small>
        </div>
      )}

      {lastResult && (
        <div style={{
          marginTop: '15px',
          padding: '12px',
          backgroundColor: lastResult.status === 'in_progress' ? '#d1ecf1' : '#d4edda',
          border: `1px solid ${lastResult.status === 'in_progress' ? '#bee5eb' : '#c3e6cb'}`,
          borderRadius: '6px',
          fontSize: '14px'
        }}>
          <strong>Last Run:</strong> {lastResult.status === 'in_progress' ? 'Started' : 'Completed'} at{' '}
          {new Date(lastResult.timestamp).toLocaleString()}
          <br />
          <strong>Dataset:</strong> {lastResult.dataset_name}
          <br />
          <strong>Models:</strong> Transcribe: {transcribeModel}, Analysis: {analysisModel}
          <br />
          <strong>Temperatures:</strong> Transcribe: {transcribeTemperature}, Analysis: {analysisTemperature}
        </div>
      )}
    </div>
  );
};

export default AIInterviewV2EvaluationRunner;
