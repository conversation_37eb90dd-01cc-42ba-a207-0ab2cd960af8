import React, { useState, useEffect } from 'react';
import PromptEditor from './components/PromptEditor';
import EvaluationRunner from './components/EvaluationRunner';
import LGDEvaluationRunner from './components/LGDEvaluationRunner';
import BEIEvaluationRunner from './components/BEIEvaluationRunner';
import EtrayEvaluationRunner from './components/EtrayEvaluationRunner';
import AIInterviewEvaluationRunner from './components/AIInterviewEvaluationRunner';
import DatasetManager from './components/DatasetManager';
import ResultsTable from './components/ResultsTable';
import LGDResultsTable from './components/LGDResultsTable';
import BEIResultsTable from './components/BEIResultsTable';
import EtrayResultsTable from './components/EtrayResultsTable';
import AIInterviewResultsTable from './components/AIInterviewResultsTable';
import Sidebar from './components/Sidebar';
import { promptsApi, evaluationsApi, lgdEvaluationsApi, beiEvaluationsApi, etrayEvaluationsApi, aiInterviewEvaluationsApi } from './services/api';

const App = () => {
  const [prompts, setPrompts] = useState([]);
  const [evaluations, setEvaluations] = useState([]);
  const [lgdEvaluations, setLgdEvaluations] = useState([]);
  const [beiEvaluations, setBeiEvaluations] = useState([]);
  const [etrayEvaluations, setEtrayEvaluations] = useState([]);
  const [aiInterviewEvaluations, setAiInterviewEvaluations] = useState([]);
  const [selectedDatasetId, setSelectedDatasetId] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeEvalType, setActiveEvalType] = useState('idp'); // 'idp', 'lgd', 'bei', 'etray', or 'ai-interview'

  // Track which data has been loaded to avoid refetching
  const [loadedData, setLoadedData] = useState({
    prompts: false,
    idp: false,
    lgd: false,
    bei: false,
    etray: false,
    aiInterview: false
  });

  useEffect(() => {
    loadInitialData();
  }, []);

  // Load prompts and IDP data on initial load (since IDP is the default tab)
  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [promptsResponse, evaluationsResponse] = await Promise.all([
        promptsApi.getAll(),
        evaluationsApi.getAll()
      ]);

      setPrompts(promptsResponse.data);
      setEvaluations(evaluationsResponse.data);
      setLoadedData(prev => ({ ...prev, prompts: true, idp: true }));
      setError(null);
    } catch (err) {
      setError('Failed to load data');
      console.error('Error loading initial data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Load LGD evaluations when needed
  const loadLGDData = async () => {
    if (loadedData.lgd) return; // Already loaded

    try {
      const lgdEvaluationsResponse = await lgdEvaluationsApi.getAll();
      setLgdEvaluations(lgdEvaluationsResponse.data);
      setLoadedData(prev => ({ ...prev, lgd: true }));
    } catch (lgdError) {
      console.warn('LGD evaluations not available yet:', lgdError);
      setLgdEvaluations([]);
      setLoadedData(prev => ({ ...prev, lgd: true })); // Mark as loaded even if empty
    }
  };

  // Load BEI evaluations when needed
  const loadBEIData = async () => {
    if (loadedData.bei) return; // Already loaded

    try {
      const beiEvaluationsResponse = await beiEvaluationsApi.getAll();
      setBeiEvaluations(beiEvaluationsResponse.data);
      setLoadedData(prev => ({ ...prev, bei: true }));
    } catch (beiError) {
      console.warn('BEI evaluations not available yet:', beiError);
      setBeiEvaluations([]);
      setLoadedData(prev => ({ ...prev, bei: true })); // Mark as loaded even if empty
    }
  };

  // Load E-tray evaluations when needed
  const loadEtrayData = async () => {
    if (loadedData.etray) return; // Already loaded

    try {
      const etrayEvaluationsResponse = await etrayEvaluationsApi.getAll();
      setEtrayEvaluations(etrayEvaluationsResponse.data);
      setLoadedData(prev => ({ ...prev, etray: true }));
    } catch (etrayError) {
      console.warn('E-tray evaluations not available yet:', etrayError);
      setEtrayEvaluations([]);
      setLoadedData(prev => ({ ...prev, etray: true })); // Mark as loaded even if empty
    }
  };

  // Load AI Interview evaluations when needed
  const loadAIInterviewData = async () => {
    if (loadedData.aiInterview) return; // Already loaded

    try {
      const aiInterviewEvaluationsResponse = await aiInterviewEvaluationsApi.getAll();
      setAiInterviewEvaluations(aiInterviewEvaluationsResponse.data);
      setLoadedData(prev => ({ ...prev, aiInterview: true }));
    } catch (aiInterviewError) {
      console.warn('AI Interview evaluations not available yet:', aiInterviewError);
      setAiInterviewEvaluations([]);
      setLoadedData(prev => ({ ...prev, aiInterview: true })); // Mark as loaded even if empty
    }
  };

  // Handle tab switching with lazy loading
  const handleEvalTypeChange = async (newEvalType) => {
    setActiveEvalType(newEvalType);

    // Load data for the new tab if not already loaded
    if (newEvalType === 'lgd' && !loadedData.lgd) {
      await loadLGDData();
    } else if (newEvalType === 'bei' && !loadedData.bei) {
      await loadBEIData();
    } else if (newEvalType === 'etray' && !loadedData.etray) {
      await loadEtrayData();
    } else if (newEvalType === 'ai-interview' && !loadedData.aiInterview) {
      await loadAIInterviewData();
    }
  };

  const handlePromptUpdate = async (id, content) => {
    try {
      const response = await promptsApi.update(id, content);
      setPrompts(prev => prev.map(p => p.id === id ? response.data : p));
      return response.data;
    } catch (err) {
      console.error('Error updating prompt:', err);
      throw err;
    }
  };

  const handleEvaluationRun = async (input) => {
    try {
      const response = await evaluationsApi.run(input);
      setEvaluations(prev => [response.data, ...prev]);
      return response.data;
    } catch (err) {
      console.error('Error running evaluation:', err);
      throw err;
    }
  };

  const handleLGDEvaluationRun = async (input) => {
    try {
      const response = await lgdEvaluationsApi.run(input);
      setLgdEvaluations(prev => [response.data, ...prev]);
      return response.data;
    } catch (err) {
      console.error('Error running LGD evaluation:', err);
      throw err;
    }
  };

  const handleBEIEvaluationRun = async (input) => {
    try {
      const response = await beiEvaluationsApi.run(input);
      setBeiEvaluations(prev => [response.data, ...prev]);
      return response.data;
    } catch (err) {
      console.error('Error running BEI evaluation:', err);
      throw err;
    }
  };

  const handleEtrayEvaluationRun = async (formData) => {
    try {
      const response = await etrayEvaluationsApi.run(formData);
      setEtrayEvaluations(prev => [response.data, ...prev]);
      return response.data;
    } catch (err) {
      console.error('Error running E-tray evaluation:', err);
      throw err;
    }
  };

  const handleAIInterviewEvaluationRun = async (datasetId) => {
    try {
      const response = await aiInterviewEvaluationsApi.run(datasetId);
      const newEvaluation = response.data;

      // Add to the beginning of the list
      setAiInterviewEvaluations(prev => [newEvaluation, ...prev]);

      return newEvaluation;
    } catch (error) {
      console.error('Error running AI Interview evaluation:', error);
      throw error;
    }
  };

  const handleLGDPollingUpdate = async (evaluationId, status) => {
    try {
      // Refresh the specific evaluation or all evaluations
      const response = await lgdEvaluationsApi.getAll();
      setLgdEvaluations(response.data);

      if (status === 'completed') {
        console.log(`LGD evaluation ${evaluationId} completed successfully`);
      } else if (status === 'error') {
        console.error(`LGD evaluation ${evaluationId} failed`);
      }
    } catch (err) {
      console.error('Error refreshing LGD evaluations after polling:', err);
    }
  };

  const handleBEIPollingUpdate = async (evaluationId, status) => {
    try {
      // Refresh the specific evaluation or all evaluations
      const response = await beiEvaluationsApi.getAll();
      setBeiEvaluations(response.data);

      if (status === 'completed') {
        console.log(`BEI evaluation ${evaluationId} completed successfully`);
      } else if (status === 'error') {
        console.error(`BEI evaluation ${evaluationId} failed`);
      }
    } catch (err) {
      console.error('Error refreshing BEI evaluations after polling:', err);
    }
  };

  const handleEtrayPollingUpdate = async (evaluationId, status) => {
    try {
      // Refresh the specific evaluation or all evaluations
      const response = await etrayEvaluationsApi.getAll();
      setEtrayEvaluations(response.data);

      if (status === 'completed') {
        console.log(`E-tray evaluation ${evaluationId} completed successfully`);
      } else if (status === 'error') {
        console.error(`E-tray evaluation ${evaluationId} failed`);
      }
    } catch (err) {
      console.error('Error refreshing E-tray evaluations after polling:', err);
    }
  };

  const handleEvaluationUpdate = (updatedEvaluation) => {
    setEvaluations(prev =>
      prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
    );
  };

  const handleLGDEvaluationUpdate = (updatedEvaluation) => {
    setLgdEvaluations(prev =>
      prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
    );
  };

  const handleBEIEvaluationUpdate = (updatedEvaluation) => {
    setBeiEvaluations(prev =>
      prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
    );
  };

  const handleEtrayEvaluationUpdate = (updatedEvaluation) => {
    setEtrayEvaluations(prev =>
      prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
    );
  };

  const handleAIInterviewPollingUpdate = async (updatedEvaluation) => {
    try {
      // Refresh all AI Interview evaluations when polling stops
      const response = await aiInterviewEvaluationsApi.getAll();
      setAiInterviewEvaluations(response.data);

      if (updatedEvaluation.status === 'completed') {
        console.log(`AI Interview evaluation ${updatedEvaluation.id} completed successfully`);
      } else if (updatedEvaluation.status === 'error') {
        console.error(`AI Interview evaluation ${updatedEvaluation.id} failed`);
      }
    } catch (err) {
      console.error('Error refreshing AI Interview evaluations after polling:', err);
      // Fallback to updating just the single evaluation if API call fails
      setAiInterviewEvaluations(prev =>
        prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
      );
    }
  };

  const handleAIInterviewEvaluationUpdate = (updatedEvaluation) => {
    setAiInterviewEvaluations(prev =>
      prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
    );
  };

  if (loading) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h2>Loading...</h2>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '20px', textAlign: 'center', color: 'red' }}>
        <h2>Error: {error}</h2>
        <button onClick={loadInitialData}>Retry</button>
      </div>
    );
  }

  // Filter prompts based on active evaluation type
  const getFilteredPrompts = () => {
    if (activeEvalType === 'idp') {
      return prompts.filter(p => p.id <= 2); // IDP prompts (1, 2)
    } else if (activeEvalType === 'lgd') {
      return prompts.filter(p => p.id === 3); // LGD prompts (only 3, hide 4)
    } else if (activeEvalType === 'bei') {
      return []; // BEI uses a fixed prompt from file system, no editable prompts
    } else if (activeEvalType === 'etray') {
      return []; // E-tray uses fixed prompts from database, no editable prompts in main view
    } else if (activeEvalType === 'ai-interview') {
      return prompts.filter(p => p.id >= 5 && p.id <= 7); // AI Interview prompts (5, 6, 7)
    }
    return [];
  };

  return (
    <div style={{ display: 'flex', minHeight: '100vh' }}>
      <Sidebar
        activeEvalType={activeEvalType}
        onEvalTypeChange={handleEvalTypeChange}
      />

      <div style={{ flex: 1, padding: '20px', maxWidth: '1400px', margin: '0 auto' }}>
        <h1 style={{ textAlign: 'center', marginBottom: '30px' }}>
          {activeEvalType === 'idp' ? 'IDP Recommendation' :
           activeEvalType === 'lgd' ? 'LGD Analysis' :
           activeEvalType === 'bei' ? 'BEI Analysis' :
           activeEvalType === 'etray' ? 'E-tray Analysis' :
           'AI Interview'} Prompt Evaluations
        </h1>

        <div style={{ display: 'grid', gap: '20px', marginBottom: '30px' }}>
          {/* Dataset Manager for AI Interview */}
          {activeEvalType === 'ai-interview' && (
            <DatasetManager
              onDatasetSelect={setSelectedDatasetId}
              selectedDatasetId={selectedDatasetId}
            />
          )}

          <div
            className="prompt-grid"
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
              gap: '20px'
            }}>
            {getFilteredPrompts().map(prompt => (
              <PromptEditor
                key={prompt.id}
                prompt={prompt}
                onUpdate={handlePromptUpdate}
              />
            ))}
          </div>

          {activeEvalType === 'idp' ? (
            <EvaluationRunner onRun={handleEvaluationRun} />
          ) : activeEvalType === 'lgd' ? (
            <LGDEvaluationRunner
              onRun={handleLGDEvaluationRun}
              onPollingUpdate={handleLGDPollingUpdate}
            />
          ) : activeEvalType === 'bei' ? (
            <BEIEvaluationRunner
              onRun={handleBEIEvaluationRun}
              onPollingUpdate={handleBEIPollingUpdate}
            />
          ) : activeEvalType === 'etray' ? (
            <EtrayEvaluationRunner
              onRun={handleEtrayEvaluationRun}
              onPollingUpdate={handleEtrayPollingUpdate}
            />
          ) : (
            <AIInterviewEvaluationRunner
              selectedDatasetId={selectedDatasetId}
              onRun={handleAIInterviewEvaluationRun}
              onPollingUpdate={handleAIInterviewPollingUpdate}
            />
          )}
        </div>

        {activeEvalType === 'idp' ? (
          <ResultsTable evaluations={evaluations} onEvaluationUpdate={handleEvaluationUpdate} />
        ) : activeEvalType === 'lgd' ? (
          <LGDResultsTable evaluations={lgdEvaluations} onEvaluationUpdate={handleLGDEvaluationUpdate} />
        ) : activeEvalType === 'bei' ? (
          <BEIResultsTable evaluations={beiEvaluations} onEvaluationUpdate={handleBEIEvaluationUpdate} />
        ) : activeEvalType === 'etray' ? (
          <EtrayResultsTable
            evaluations={etrayEvaluations}
            onAnnotationUpdate={async (id, annotation) => {
              await etrayEvaluationsApi.updateAnnotation(id, annotation);
              handleEtrayEvaluationUpdate({ ...etrayEvaluations.find(e => e.id === id), annotation });
            }}
          />
        ) : (
          <AIInterviewResultsTable evaluations={aiInterviewEvaluations} onEvaluationUpdate={handleAIInterviewEvaluationUpdate} />
        )}
      </div>
    </div>
  );
};

export default App;
