const express = require('express');
const router = express.Router();
const aiInterviewV2GeminiService = require('../services/aiInterviewV2Gemini');
const supabase = require('../supabaseClient');

// Background processing function
async function processAIInterviewV2EvaluationAsync(
  evaluationId, 
  dataset, 
  prompt1Content, 
  prompt2Content,
  transcribeModel,
  transcribeTemperature,
  analysisModel,
  analysisTemperature
) {
  try {
    console.log(`Starting background processing for AI Interview V2 evaluation ${evaluationId}`);

    // Run the AI Interview V2 prompt chain
    const result = await aiInterviewV2GeminiService.runAIInterviewV2PromptChain(
      dataset, 
      prompt1Content, 
      prompt2Content,
      transcribeModel,
      transcribeTemperature,
      analysisModel,
      analysisTemperature
    );

    // Update the record with the result
    const { error: updateError } = await supabase
      .from('ai_interview_v2_evaluations')
      .update({
        output: result.finalOutput,
        status: 'completed',
        details: result
      })
      .eq('id', evaluationId);

    if (updateError) {
      console.error('Error updating AI Interview V2 evaluation:', updateError);
      // Try to update with error status
      await supabase
        .from('ai_interview_v2_evaluations')
        .update({
          status: 'error',
          details: { error: 'Failed to update evaluation result' }
        })
        .eq('id', evaluationId);
    } else {
      console.log(`AI Interview V2 evaluation ${evaluationId} completed successfully`);
    }
  } catch (error) {
    console.error(`Error processing AI Interview V2 evaluation ${evaluationId}:`, error);
    
    // Update the record with error status
    const { error: updateError } = await supabase
      .from('ai_interview_v2_evaluations')
      .update({
        status: 'error',
        details: { 
          error: error.message,
          stack: error.stack 
        }
      })
      .eq('id', evaluationId);

    if (updateError) {
      console.error('Error updating AI Interview V2 evaluation with error status:', updateError);
    }
  }
}

// GET all AI Interview V2 evaluations
router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('ai_interview_v2_evaluations')
      .select('*')
      .order('timestamp', { ascending: false });

    if (error) {
      console.error('Error fetching AI Interview V2 evaluations:', error);
      return res.status(500).json({ error: 'Failed to fetch AI Interview V2 evaluations' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /ai-interview-v2-evaluations:', error);
    res.status(500).json({ error: 'Failed to fetch AI Interview V2 evaluations' });
  }
});

// GET specific AI Interview V2 evaluation status
router.get('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    
    const { data, error } = await supabase
      .from('ai_interview_v2_evaluations')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching AI Interview V2 evaluation status:', error);
      return res.status(404).json({ error: 'AI Interview V2 evaluation not found' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /ai-interview-v2-evaluations/:id/status:', error);
    res.status(500).json({ error: 'Failed to fetch AI Interview V2 evaluation status' });
  }
});

// PUT update annotation for AI Interview V2 evaluation
router.put('/:id/annotation', async (req, res) => {
  try {
    const { id } = req.params;
    const { annotation } = req.body;

    const { data, error } = await supabase
      .from('ai_interview_v2_evaluations')
      .update({ annotation })
      .eq('id', id)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating AI Interview V2 evaluation annotation:', error);
      return res.status(500).json({ error: 'Failed to update annotation' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in PUT /ai-interview-v2-evaluations/:id/annotation:', error);
    res.status(500).json({ error: 'Failed to update annotation' });
  }
});

// POST run new AI Interview V2 evaluation
router.post('/run', async (req, res) => {
  try {
    const { 
      datasetId, 
      transcribeModel = 'gemini-2.5-pro',
      transcribeTemperature = 0.3,
      analysisModel = 'gemini-2.5-pro',
      analysisTemperature = 0.3
    } = req.body;

    if (!datasetId) {
      return res.status(400).json({ error: 'Dataset ID is required' });
    }

    // Validate model options
    const validModels = ['gemini-2.5-pro', 'gemini-2.5-flash'];
    if (!validModels.includes(transcribeModel) || !validModels.includes(analysisModel)) {
      return res.status(400).json({ error: 'Invalid model selection' });
    }

    // Validate temperature range
    if (transcribeTemperature < 0 || transcribeTemperature > 1 || 
        analysisTemperature < 0 || analysisTemperature > 1) {
      return res.status(400).json({ error: 'Temperature must be between 0 and 1' });
    }

    // Fetch the dataset
    const { data: dataset, error: datasetError } = await supabase
      .from('datasets')
      .select('*')
      .eq('id', datasetId)
      .single();

    if (datasetError || !dataset) {
      console.error('Error fetching dataset:', datasetError);
      return res.status(400).json({ error: 'Dataset not found' });
    }

    // Validate dataset structure
    if (!dataset.data || !Array.isArray(dataset.data.data)) {
      return res.status(400).json({ error: 'Invalid dataset structure' });
    }

    // Fetch prompts (ID 11 for transcription, ID 12 for analysis)
    const { data: prompts, error: promptsError } = await supabase
      .from('prompts')
      .select('*')
      .in('id', [11, 12])
      .order('id');

    if (promptsError || !prompts || prompts.length !== 2) {
      console.error('Error fetching AI Interview V2 prompts:', promptsError);
      return res.status(500).json({ error: 'Failed to fetch AI Interview V2 prompts' });
    }

    const prompt1 = prompts.find(p => p.id === 11); // Transcription prompt
    const prompt2 = prompts.find(p => p.id === 12); // Analysis prompt

    if (!prompt1 || !prompt2) {
      return res.status(500).json({ error: 'AI Interview V2 prompts not found' });
    }

    // Create initial record with "in_progress" status
    const evaluationToInsert = {
      dataset_id: datasetId,
      dataset_name: dataset.name,
      output: null,
      status: 'in_progress',
      prompt1Version: prompt1.version,
      prompt2Version: prompt2.version,
      prompt1Content: prompt1.content,
      prompt2Content: prompt2.content,
      timestamp: new Date().toISOString(),
      details: {
        models: {
          transcribe: transcribeModel,
          analysis: analysisModel
        },
        temperatures: {
          transcribe: transcribeTemperature,
          analysis: analysisTemperature
        }
      }
    };

    const { data: newEvaluation, error: insertError } = await supabase
      .from('ai_interview_v2_evaluations')
      .insert([evaluationToInsert])
      .select('*')
      .single();

    if (insertError) {
      console.error('Error inserting AI Interview V2 evaluation:', insertError);
      return res.status(500).json({ error: 'Failed to save AI Interview V2 evaluation' });
    }

    // Start background processing
    processAIInterviewV2EvaluationAsync(
      newEvaluation.id, 
      dataset, 
      prompt1.content, 
      prompt2.content,
      transcribeModel,
      transcribeTemperature,
      analysisModel,
      analysisTemperature
    );

    // Return the evaluation record immediately
    res.json(newEvaluation);
  } catch (error) {
    console.error('Error in POST /ai-interview-v2-evaluations/run:', error);
    res.status(500).json({ error: 'Failed to run AI Interview V2 evaluation' });
  }
});

module.exports = router;
