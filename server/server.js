const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

const promptRoutes = require('./routes/prompts');
const evaluationRoutes = require('./routes/evaluations');
const lgdEvaluationRoutes = require('./routes/lgdEvaluations');
const beiEvaluationRoutes = require('./routes/beiEvaluations');
const etrayEvaluationRoutes = require('./routes/etrayEvaluations');
const datasetRoutes = require('./routes/datasets');
const aiInterviewEvaluationRoutes = require('./routes/aiInterviewEvaluations');
const aiInterviewV2EvaluationRoutes = require('./routes/aiInterviewV2Evaluations');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Request logging middleware
app.use('/api', (req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.path}`);
  next();
});

// Routes

// Basic Authentication Middleware
const basicAuth = require('express-basic-auth');

const authMiddleware = basicAuth({
  users: { [process.env.BASIC_AUTH_USERNAME]: process.env.BASIC_AUTH_PASSWORD },
  challenge: true, // Show authentication dialog
  unauthorizedResponse: 'Unauthorized'
});

// Apply basic auth to all routes except health check
app.use('/api/*', (req, res, next) => {
  if (req.path === '/api/health') {
    return next();
  }
  authMiddleware(req, res, next);
});

app.use('/api/prompts', promptRoutes);
app.use('/api/evaluations', evaluationRoutes);
app.use('/api/lgd-evaluations', lgdEvaluationRoutes);
app.use('/api/bei-evaluations', beiEvaluationRoutes);
app.use('/api/etray-evaluations', etrayEvaluationRoutes);
app.use('/api/datasets', datasetRoutes);
app.use('/api/ai-interview-evaluations', aiInterviewEvaluationRoutes);
app.use('/api/ai-interview-v2-evaluations', aiInterviewV2EvaluationRoutes);

// Serve static files from data directory
app.use('/api/data', express.static(path.join(__dirname, 'data')));

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is running' });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
