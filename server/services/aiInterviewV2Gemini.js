const fs = require('fs').promises;
const path = require('path');

class AIInterviewV2GeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
    }
    return this.client;
  }

  async generateResponse(model, prompt, systemPrompt, temperature, responseFormat = "text/plain") {
    try {
      await this.initPromise;
      const response = await this.client.models.generateContent({
        model: model,
        contents: prompt,
        config: {
          temperature: temperature,
          responseMimeType: responseFormat,
          systemInstruction: systemPrompt
        }
      });
      return response.text;
    } catch (error) {
      console.error('Error generating response:', error);
      throw error;
    }
  }

  async uploadFileToGemini(fileBuffer, mimeType, displayName) {
    try {
      await this.initPromise;
      
      const uploadResponse = await this.client.files.upload({
        file: {
          data: fileBuffer,
          mimeType: mimeType
        },
        name: displayName
      });

      return {
        gemini_uri: uploadResponse.file.uri,
        mime_type: mimeType,
        display_name: displayName
      };
    } catch (error) {
      console.error('Error uploading file to Gemini:', error);
      throw error;
    }
  }

  async checkFileStatus(fileUri) {
    try {
      await this.initPromise;
      const response = await this.client.files.get(fileUri);
      return response.state;
    } catch (error) {
      console.error('Error checking file status:', error);
      throw error;
    }
  }

  async transcribeVideo(videoUrl, transcribePrompt, model = 'gemini-2.5-pro', temperature = 0.3) {
    try {
      console.log(`Transcribing video: ${videoUrl}`);

      // Download video from S3 URL
      const response = await fetch(videoUrl);
      if (!response.ok) {
        throw new Error(`Failed to download video: ${response.statusText}`);
      }

      const videoBuffer = Buffer.from(await response.arrayBuffer());
      const mimeType = response.headers.get('content-type') || 'video/mp4';

      // Upload to Gemini
      const geminiFile = await this.uploadFileToGemini(
        videoBuffer,
        mimeType,
        `video_${Date.now()}`
      );

      // Wait for file to be processed
      let fileStatus = await this.checkFileStatus(geminiFile.gemini_uri);
      let max_retry = 10;

      while (fileStatus !== 'ACTIVE' && max_retry > 0) {
        max_retry -= 1;
        fileStatus = await this.checkFileStatus(geminiFile.gemini_uri);
        await new Promise(resolve => setTimeout(resolve, 10000)); // Wait for 10 seconds
      }

      if (fileStatus !== 'ACTIVE') {
        throw new Error(`Failed to activate Gemini file after retries`);
      }

      // Create prompt with video file
      const prompt = [
        {
          role: 'user',
          parts: [
            {
              fileData: {
                mimeType: geminiFile.mime_type,
                fileUri: geminiFile.gemini_uri
              }
            }
          ]
        }
      ];

      const response_text = await this.generateResponse(
        model,
        prompt,
        transcribePrompt,
        temperature,
        "text/plain"
      );

      // Extract transcript from response
      const transcriptMatch = response_text.match(/Transcripted Text:\s*(.+)/i);
      const transcript = transcriptMatch ? transcriptMatch[1].trim() : response_text.trim();

      return {
        transcript: transcript,
        model: model,
        temperature: temperature
      };
    } catch (error) {
      console.error('Error transcribing video:', error);
      throw error;
    }
  }

  async analyzeCompiledTranscript(compiledTranscript, analysisPrompt, model = 'gemini-2.5-pro', temperature = 0.3) {
    try {
      console.log('Analyzing compiled transcript...');

      const prompt = [
        {
          role: 'user',
          parts: [{ text: compiledTranscript }]
        }
      ];

      const response = await this.generateResponse(
        model,
        prompt,
        analysisPrompt,
        temperature,
        "application/json"
      );

      // Parse JSON response
      let analysisResult;
      try {
        analysisResult = JSON.parse(response);
      } catch (parseError) {
        console.error('Error parsing analysis JSON:', parseError);
        throw new Error('Failed to parse analysis response as JSON');
      }

      return {
        analysis: analysisResult,
        model: model,
        temperature: temperature
      };
    } catch (error) {
      console.error('Error analyzing compiled transcript:', error);
      throw error;
    }
  }

  async runAIInterviewV2PromptChain(
    dataset, 
    transcribePrompt, 
    analysisPrompt,
    transcribeModel = 'gemini-2.5-pro',
    transcribeTemperature = 0.3,
    analysisModel = 'gemini-2.5-pro', 
    analysisTemperature = 0.3
  ) {
    try {
      console.log('Starting AI Interview V2 evaluation chain...');

      const transcriptions = [];
      const questionAnswerPairs = dataset.data || [];

      // Step 1: Transcribe each video
      console.log(`Step 1: Transcribing ${questionAnswerPairs.length} videos...`);
      for (let i = 0; i < questionAnswerPairs.length; i++) {
        const pair = questionAnswerPairs[i];
        console.log(`Transcribing video ${i + 1}/${questionAnswerPairs.length}: ${pair.question}`);

        try {
          const transcriptionResult = await this.transcribeVideo(
            pair.answer_url,
            transcribePrompt,
            transcribeModel,
            transcribeTemperature
          );

          transcriptions.push({
            question: pair.question,
            transcript: transcriptionResult.transcript,
            model: transcriptionResult.model,
            temperature: transcriptionResult.temperature
          });
        } catch (error) {
          console.error(`Error transcribing video ${i + 1}:`, error);
          transcriptions.push({
            question: pair.question,
            transcript: 'Error: Failed to transcribe video',
            model: transcribeModel,
            temperature: transcribeTemperature,
            error: error.message
          });
        }
      }

      // Step 2: Compile transcripts into structured format
      console.log('Step 2: Compiling transcripts...');
      let compiledTranscript = '';
      transcriptions.forEach((item, index) => {
        compiledTranscript += `Question ${index + 1}: ${item.question}\n`;
        compiledTranscript += `Answer Transcript: ${item.transcript}\n\n`;
      });

      // Step 3: Analyze compiled transcript
      console.log('Step 3: Analyzing compiled transcript...');
      const analysisResult = await this.analyzeCompiledTranscript(
        compiledTranscript,
        analysisPrompt,
        analysisModel,
        analysisTemperature
      );

      // Combine all results
      const finalResult = {
        transcriptions,
        compiledTranscript,
        analysis: analysisResult.analysis,
        finalOutput: {
          evaluations: analysisResult.analysis,
          transcriptions: transcriptions,
          compiledTranscript: compiledTranscript
        },
        models: {
          transcribe: transcribeModel,
          analysis: analysisModel
        },
        temperatures: {
          transcribe: transcribeTemperature,
          analysis: analysisTemperature
        }
      };

      console.log('AI Interview V2 evaluation chain completed successfully');
      return finalResult;
    } catch (error) {
      console.error('Error in AI Interview V2 prompt chain:', error);
      throw error;
    }
  }
}

module.exports = new AIInterviewV2GeminiService();
