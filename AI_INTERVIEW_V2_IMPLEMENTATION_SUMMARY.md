# AI Interview V2 Implementation Summary

## Overview

AI Interview V2 is a new evaluation system that provides a simplified approach to analyzing interview videos. Unlike the original AI Interview system that uses a 3-step evaluation chain (question evaluator → summary evaluator → insight summary), AI Interview V2 uses a streamlined 2-step process:

1. **Video Transcription**: Each answer video is transcribed using a system prompt
2. **Compiled Analysis**: All transcriptions are compiled into a structured format and analyzed for competency assessment

## Key Features

- **Model Selection**: Users can choose between `gemini-2.5-pro` and `gemini-2.5-flash` for both transcription and analysis steps
- **Temperature Control**: Adjustable temperature settings (0-1) for both transcription and analysis prompts
- **Background Processing**: Asynchronous evaluation with status tracking and polling
- **Annotation System**: Users can mark results as "good" or "not good"
- **Real-time Status Updates**: Frontend polls every 30 seconds for completion status

## Database Schema

### AI Interview V2 Evaluations Table
- **Migration**: `supabase/migrations/2025_07_10_10_38_00_create_ai_interview_v2_tables.sql`
- **Purpose**: Store AI Interview V2 evaluation results
- **Schema**:
  - `id`: Primary key
  - `dataset_id`: Reference to dataset
  - `dataset_name`: Cached dataset name
  - `output`: JSONB evaluation results
  - `status`: Evaluation status (in_progress, completed, error)
  - `prompt1Version`, `prompt2Version`: Prompt versions used
  - `prompt1Content`, `prompt2Content`: Cached prompt content
  - `timestamp`: Evaluation timestamp
  - `details`: JSONB processing details (models, temperatures, etc.)
  - `annotation`: User annotation (good/not good)

### Prompts
- **Migration**: `supabase/migrations/2025_07_10_10_39_00_insert_ai_interview_v2_prompts.sql`
- **Prompt ID 11**: AI Interview V2 Transcription Prompt
- **Prompt ID 12**: AI Interview V2 Analysis Prompt

## Backend Services

### AI Interview V2 Gemini Service
- **File**: `server/services/aiInterviewV2Gemini.js`
- **Purpose**: Handle the 2-step evaluation process
- **Features**:
  - Video download from S3 URLs
  - File upload to Gemini API
  - Video transcription with configurable models/temperature
  - Transcript compilation into structured format
  - Competency analysis with JSON response parsing
  - Error handling and recovery

### AI Interview V2 Routes
- **File**: `server/routes/aiInterviewV2Evaluations.js`
- **Purpose**: API endpoints for AI Interview V2 evaluations
- **Features**:
  - Background processing with async workers
  - Model and temperature parameter validation
  - Status tracking and polling endpoints
  - Annotation update functionality
  - Integration with dataset system

## Frontend Components

### AI Interview V2 Evaluation Runner
- **File**: `client/src/components/AIInterviewV2EvaluationRunner.jsx`
- **Purpose**: Interface for running AI Interview V2 evaluations
- **Features**:
  - Dataset selection integration
  - Model selection dropdowns (gemini-2.5-pro/flash)
  - Temperature sliders for both transcription and analysis
  - Real-time status display
  - Background processing indicators

### AI Interview V2 Results Table
- **File**: `client/src/components/AIInterviewV2ResultsTable.jsx`
- **Purpose**: Display evaluation results and manage annotations
- **Features**:
  - Expandable result rows
  - Competency score visualization
  - Question-by-question analysis breakdown
  - Model and temperature information display
  - Annotation functionality (good/not good)
  - Prompt viewing modal

## Integration Points

### API Client
- **File**: `client/src/services/api.js`
- **Features**:
  - `aiInterviewV2EvaluationsApi` with full CRUD operations
  - Model and temperature parameter support

### Server Configuration
- **File**: `server/server.js`
- **Changes**:
  - Added AI Interview V2 routes integration

### Main App Integration
- **File**: `client/src/App.jsx`
- **Features**:
  - Lazy loading for AI Interview V2 data
  - State management for evaluations
  - Routing and navigation support
  - Polling update handlers

### Sidebar Navigation
- **File**: `client/src/components/Sidebar.jsx`
- **Features**:
  - AI Interview V2 menu option
  - Active state management

## Data Flow

### AI Interview V2 Evaluation Process:
1. User selects dataset containing question-answer pairs
2. User configures models and temperatures for transcription and analysis
3. User initiates evaluation through AIInterviewV2EvaluationRunner
4. Backend creates evaluation record with "in_progress" status
5. Background worker processes the 2-step evaluation:
   - **Step 1**: Transcribe each video answer to text using selected model/temperature
   - **Step 2**: Compile all transcriptions into structured format
   - **Step 3**: Analyze compiled transcript for competency assessment using selected model/temperature
6. Frontend polls for status updates every 30 seconds
7. Results are displayed in AIInterviewV2ResultsTable
8. Users can annotate results and view detailed outputs

## Key Differences from AI Interview V1

| Feature | AI Interview V1 | AI Interview V2 |
|---------|----------------|-----------------|
| **Process Steps** | 3-step chain (question → summary → insight) | 2-step process (transcribe → analyze) |
| **Model Selection** | Fixed models | User-selectable models |
| **Temperature Control** | Fixed temperature | User-configurable temperature |
| **Transcript Handling** | Individual question analysis | Compiled transcript analysis |
| **Prompt IDs** | 5, 6, 7 | 11, 12 |
| **Analysis Approach** | Question-by-question then aggregation | Holistic analysis of compiled transcript |

## Prompt Structure

### Transcription Prompt (ID 11)
- **Purpose**: Convert video content to text
- **Format**: Simple transcription with "Transcripted Text: String" format
- **Model Options**: gemini-2.5-pro, gemini-2.5-flash
- **Temperature**: User configurable (0-1)

### Analysis Prompt (ID 12)
- **Purpose**: Analyze compiled transcripts for competency assessment
- **Competencies**: Manajemen Krisis, Kepercayaan diri, Komunikasi
- **Scoring**: 1-5 scale with detailed indicators and examples
- **Output**: JSON format with scores, analysis, and summary
- **Model Options**: gemini-2.5-pro, gemini-2.5-flash
- **Temperature**: User configurable (0-1)

## Configuration Options

### Model Options
- **gemini-2.5-pro**: Higher accuracy, slower processing
- **gemini-2.5-flash**: Faster processing, good accuracy

### Temperature Settings
- **Range**: 0.0 to 1.0
- **0.0**: Deterministic, consistent outputs
- **1.0**: Creative, varied outputs
- **Default**: 0.3 for both transcription and analysis

## Error Handling

- **Video Download Failures**: Graceful handling with error messages in transcription results
- **Gemini API Errors**: Retry logic and fallback error responses
- **JSON Parsing Errors**: Validation and error reporting for analysis results
- **File Processing Timeouts**: Configurable retry limits with status tracking

## Sample Data Structure

The compiled transcript format used in V2:
```
Question 1: [Question text]
Answer Transcript: [Transcribed answer text]

Question 2: [Question text]
Answer Transcript: [Transcribed answer text]

...
```

## Future Enhancements

- **Batch Processing**: Support for processing multiple datasets simultaneously
- **Custom Prompts**: Allow users to edit prompts directly in the interface
- **Export Functionality**: Export results to various formats (PDF, Excel, etc.)
- **Comparison Tools**: Compare results between different model/temperature configurations
- **Advanced Analytics**: Trend analysis and performance metrics across evaluations